"use client"

import { useEffect, useState } from "react"
import <PERSON><PERSON> from "./ui/Button"
import { <PERSON>, CardContent, CardHeader } from "./ui/Card"
import { CheckCircle, Package, Truck, Clock, X } from "lucide-react"

interface SuccessModalProps {
  isOpen: boolean
  onClose: () => void
  orderId: string
  message: string
  onViewOrder: () => void
}

export default function SuccessModal({ isOpen, onClose, orderId, message, onViewOrder }: SuccessModalProps) {
  const [showContent, setShowContent] = useState(false)

  useEffect(() => {
    if (isOpen) {
      // Delay content animation slightly after modal opens
      const timer = setTimeout(() => setShowContent(true), 100)
      return () => clearTimeout(timer)
    } else {
      setShowContent(false)
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300"
        onClick={onClose}
      />

      {/* Modal */}
      <Card
        className={`relative w-full max-w-md mx-4 transform transition-all duration-300 ${
          showContent ? "scale-100 opacity-100" : "scale-95 opacity-0"
        }`}
      >
        <CardHeader className="text-center pb-4">
          <Button variant="ghost" size="sm" className="absolute right-2 top-2 h-8 w-8 p-0" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>

          {/* Success Animation */}
          <div className="mx-auto mb-4">
            <div
              className={`relative transition-all duration-500 ${
                showContent ? "scale-100 rotate-0" : "scale-0 rotate-180"
              }`}
            >
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
              <div className="absolute inset-0 w-16 h-16 border-4 border-green-200 rounded-full animate-ping" />
            </div>
          </div>

          <h2 className="text-2xl font-bold text-green-600 mb-2">Đặt hàng thành công!</h2>
          <p className="text-gray-600 mb-4">{message}</p>

          {/* Order ID */}
          <div className="bg-gray-50 rounded-lg p-3 mb-4">
            <p className="text-sm text-gray-600">Mã đơn hàng</p>
            <p className="font-mono font-bold text-lg text-blue-600">{orderId}</p>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Order Status Steps */}
          <div className="space-y-3">
            <div className="flex items-center gap-3 text-green-600">
              <CheckCircle className="w-5 h-5" />
              <span className="text-sm">Đơn hàng đã được xác nhận</span>
            </div>
            <div className="flex items-center gap-3 text-gray-400">
              <Package className="w-5 h-5" />
              <span className="text-sm">Đang chuẩn bị hàng</span>
            </div>
            <div className="flex items-center gap-3 text-gray-400">
              <Truck className="w-5 h-5" />
              <span className="text-sm">Đang giao hàng</span>
            </div>
            <div className="flex items-center gap-3 text-gray-400">
              <CheckCircle className="w-5 h-5" />
              <span className="text-sm">Giao hàng thành công</span>
            </div>
          </div>

          {/* Estimated Delivery */}
          <div className="bg-blue-50 rounded-lg p-3 flex items-center gap-3">
            <Clock className="w-5 h-5 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-blue-800">Dự kiến giao hàng</p>
              <p className="text-xs text-blue-600">Trong ngày hôm nay</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button variant="outline" className="flex-1 bg-transparent" onClick={onClose}>
              Tiếp tục mua sắm
            </Button>
            <Button className="flex-1 bg-blue-600 hover:bg-blue-700" onClick={onViewOrder}>
              Xem đơn hàng
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}