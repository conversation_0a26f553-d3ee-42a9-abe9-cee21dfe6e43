import React from 'react';

interface SeparatorProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: 'horizontal' | 'vertical';
  decorative?: boolean;
}

const Separator: React.FC<SeparatorProps> = ({
  orientation = 'horizontal',
  decorative = true,
  className = '',
  ...props
}) => {
  const baseClasses = 'shrink-0 bg-gray-200';
  
  const orientationClasses = {
    horizontal: 'h-[1px] w-full',
    vertical: 'h-full w-[1px]',
  };
  
  const classes = `${baseClasses} ${orientationClasses[orientation]} ${className}`;
  
  return (
    <div
      role={decorative ? 'none' : 'separator'}
      aria-orientation={decorative ? undefined : orientation}
      className={classes}
      {...props}
    />
  );
};

export default Separator;
