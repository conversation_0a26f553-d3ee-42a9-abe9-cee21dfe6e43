"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON> } from "./ui"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/Card"
import { Separator } from "./ui/Separator"
import { RadioGroup, RadioGroupItem } from "./ui/RadioGroup"
import { Label } from "./ui/Label"
import { Truck, CreditCard, Wallet, Clock, Zap, Building2, Smartphone, AlertCircle } from "lucide-react"
import { AddressForm } from "./Address"
import SuccessModal from "./success-modal"
import { useNavigate } from "react-router-dom"

interface BuyProductProps {
  onLogin?: () => void
  onPlaceOrder?: () => void
}

interface ValidationErrors {
  address?: string
  phone?: string
  shipping?: string
  payment?: string
}

interface Address {
  id?: number;
  name: string;
  phone: string;
  province: string;
  district: string;
  ward: string;
  detail: string;
  type: "home" | "office";
  isDefault: boolean;
  provinceCode: string;
  districtCode: string;
  wardCode: string;
}

export default function BuyProduct({ onPlaceOrder }: BuyProductProps) {
  const [selectedDelivery, setSelectedDelivery] = useState("fast")
  const [selectedPayment, setSelectedPayment] = useState("cash")
  const [showAddressForm, setShowAddressForm] = useState(false)
  const [address, setAddress] = useState<Address>({
    name: "",
    phone: "",
    province: "",
    district: "",
    ward: "",
    detail: "",
  type: "home",
    isDefault: false,
  provinceCode: "",
  districtCode: "",
  wardCode: "",
  })
  const [selectedOffer, setSelectedOffer] = useState<number | null>(null)
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [isValidating, setIsValidating] = useState(false)
  const [isProcessingOrder, setIsProcessingOrder] = useState(false)
  const [orderResult, setOrderResult] = useState<{ orderId?: string; message: string } | null>(null)
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const navigate = useNavigate();

  const validatePhoneNumber = (phone: string): boolean => {
    const phoneRegex = /^(0|\+84)[3-9][0-9]{8}$/
    return phoneRegex.test(phone.replace(/\s/g, ""))
  }

  const validateForm = (): ValidationErrors => {
    const newErrors: ValidationErrors = {}

    // Validate address
    if (!address.name || !address.phone || !address.detail) {
      newErrors.address = "Vui lòng điền đầy đủ thông tin địa chỉ giao hàng"
    }

    // Validate phone number
    if (address.phone && !validatePhoneNumber(address.phone)) {
      newErrors.phone = "Số điện thoại không hợp lệ (VD: 0901234567)"
    }

    // Validate shipping method
    if (!selectedDelivery) {
      newErrors.shipping = "Vui lòng chọn phương thức giao hàng"
    }

    // Validate payment method
    if (!selectedPayment) {
      newErrors.payment = "Vui lòng chọn phương thức thanh toán"
    }

    return newErrors
  }

  const isFormValid = (): boolean => {
    const validationErrors = validateForm()
    return Object.keys(validationErrors).length === 0
  }

  useEffect(() => {
    const selectedAddress = localStorage.getItem("selectedAddress")
    if (selectedAddress) {
      try {
        const parsedSelected = JSON.parse(selectedAddress)
        console.log("[v0] Using selected address from address page:", parsedSelected)
        setAddress(parsedSelected)

        if (
          (!parsedSelected.province || !parsedSelected.district || !parsedSelected.ward) &&
          (parsedSelected.provinceCode || parsedSelected.districtCode || parsedSelected.wardCode)
        ) {
          console.log("[v0] Fetching province/district/ward names from codes:", {
            provinceCode: parsedSelected.provinceCode,
            districtCode: parsedSelected.districtCode,
            wardCode: parsedSelected.wardCode,
          })

          Promise.all([
            parsedSelected.provinceCode
              ? fetch(`https://provinces.open-api.vn/api/p/${parsedSelected.provinceCode}`)
                  .then((res) => {
                    console.log("[v0] Province API response status:", res.status)
                    if (!res.ok) {
                      throw new Error(`Province API failed with status ${res.status}`)
                    }
                    return res.json()
                  })
                  .then((data) => {
                    console.log("[v0] Province API full response:", data)
                    return data
                  })
                  .catch((error) => {
                    console.log("[v0] Province API error:", error)
                    return { name: `Tỉnh ${parsedSelected.provinceCode}` }
                  })
              : Promise.resolve({ name: "" }),
            parsedSelected.districtCode
              ? fetch(`https://provinces.open-api.vn/api/d/${parsedSelected.districtCode}`)
                  .then((res) => {
                    console.log("[v0] District API response status:", res.status)
                    if (!res.ok) {
                      throw new Error(`District API failed with status ${res.status}`)
                    }
                    return res.json()
                  })
                  .then((data) => {
                    console.log("[v0] District API full response:", data)
                    return data
                  })
                  .catch((error) => {
                    console.log("[v0] District API error:", error)
                    return { name: `Quận ${parsedSelected.districtCode}` }
                  })
              : Promise.resolve({ name: "" }),
            parsedSelected.wardCode
              ? fetch(`https://provinces.open-api.vn/api/w/${parsedSelected.wardCode}`)
                  .then((res) => {
                    console.log("[v0] Ward API response status:", res.status)
                    if (!res.ok) {
                      throw new Error(`Ward API failed with status ${res.status}`)
                    }
                    return res.json()
                  })
                  .then((data) => {
                    console.log("[v0] Ward API full response:", data)
                    return data
                  })
                  .catch((error) => {
                    console.log("[v0] Ward API error:", error)
                    return { name: `Phường ${parsedSelected.wardCode}` }
                  })
              : Promise.resolve({ name: "" }),
          ]).then(([province, district, ward]) => {
            console.log("[v0] Final address names:", {
              province: province.name,
              district: district.name,
              ward: ward.name,
            })
            setAddress((prev) => ({
              ...prev,
              province: province.name || prev.province,
              district: district.name || prev.district,
              ward: ward.name || prev.ward,
            }))
          })
        }
        return
      } catch (error) {
        console.log("[v0] Error parsing selected address:", error)
      }
    }

    const savedAddresses = localStorage.getItem("addressList")
    if (savedAddresses) {
      try {
        const addresses = JSON.parse(savedAddresses)
        // Find default address or use first address
  const defaultAddress = addresses.find((addr: Address) => addr.isDefault) || addresses[0]

        if (defaultAddress) {
          console.log("[v0] Using default/first address from list:", defaultAddress)
          setAddress(defaultAddress)

          if (
            (!defaultAddress.province || !defaultAddress.district || !defaultAddress.ward) &&
            (defaultAddress.provinceCode || defaultAddress.districtCode || defaultAddress.wardCode)
          ) {
            console.log("[v0] Fetching province/district/ward names for default address:", {
              provinceCode: defaultAddress.provinceCode,
              districtCode: defaultAddress.districtCode,
              wardCode: defaultAddress.wardCode,
            })

            Promise.all([
              defaultAddress.provinceCode
                ? fetch(`https://provinces.open-api.vn/api/p/${defaultAddress.provinceCode}`)
                    .then((res) => {
                      console.log("[v0] Province API response status:", res.status)
                      if (!res.ok) {
                        throw new Error(`Province API failed with status ${res.status}`)
                      }
                      return res.json()
                    })
                    .then((data) => {
                      console.log("[v0] Province API full response:", data)
                      return data
                    })
                    .catch((error) => {
                      console.log("[v0] Province API error for default:", error)
                      return { name: `Tỉnh ${defaultAddress.provinceCode}` }
                    })
                : Promise.resolve({ name: "" }),
              defaultAddress.districtCode
                ? fetch(`https://provinces.open-api.vn/api/d/${defaultAddress.districtCode}`)
                    .then((res) => {
                      console.log("[v0] District API response status:", res.status)
                      if (!res.ok) {
                        throw new Error(`District API failed with status ${res.status}`)
                      }
                      return res.json()
                    })
                    .then((data) => {
                      console.log("[v0] District API full response:", data)
                      return data
                    })
                    .catch((error) => {
                      console.log("[v0] District API error for default:", error)
                      return { name: `Quận ${defaultAddress.districtCode}` }
                    })
                : Promise.resolve({ name: "" }),
              defaultAddress.wardCode
                ? fetch(`https://provinces.open-api.vn/api/w/${defaultAddress.wardCode}`)
                    .then((res) => {
                      console.log("[v0] Ward API response status:", res.status)
                      if (!res.ok) {
                        throw new Error(`Ward API failed with status ${res.status}`)
                      }
                      return res.json()
                    })
                    .then((data) => {
                      console.log("[v0] Ward API full response:", data)
                      return data
                    })
                    .catch((error) => {
                      console.log("[v0] Ward API error for default:", error)
                      return { name: `Phường ${defaultAddress.wardCode}` }
                    })
                : Promise.resolve({ name: "" }),
            ]).then(([province, district, ward]) => {
              console.log("[v0] Final default address names:", {
                province: province.name,
                district: district.name,
                ward: ward.name,
              })
              setAddress((prev) => ({
                ...prev,
                province: province.name || prev.province,
                district: district.name || prev.district,
                ward: ward.name || prev.ward,
              }))
            })
          }
        }
      } catch (error) {
        console.log("[v0] Error parsing saved addresses:", error)
      }
    }
  }, [])

  // removed useRouter, using navigate from react-router-dom

  async function onPlaceOrderClick() {
    setIsValidating(true)
    const validationErrors = validateForm()

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      setIsValidating(false)
      return
    }

    setErrors({})
    setIsProcessingOrder(true)

    try {
      console.log("[v0] Form validation passed, creating order...")

      // Prepare order data
      const selectedShipping = shippingOptions.find((opt) => opt.id === selectedDelivery)
      const selectedPaymentMethod = paymentMethods.find((method) => method.id === selectedPayment)

      const orderData = {
        customerInfo: {
          name: address.name,
          phone: address.phone,
        },
        shippingAddress: {
          name: address.name,
          phone: address.phone,
          detail: address.detail,
          ward: address.ward,
          district: address.district,
          province: address.province,
          type: address.type,
        },
        shippingMethod: {
          id: selectedDelivery,
          name: selectedShipping?.name || "",
          price: Number.parseFloat(selectedShipping?.price.replace(/[^\d]/g, "") || "0"),
          estimatedTime: selectedShipping?.estimatedTime || "",
        },
        paymentMethod: {
          id: selectedPayment,
          name: selectedPaymentMethod?.name || "",
        },
        items: [
          {
            id: "book-001",
            name: "Chat GPT Thực Chiến",
            price: 110000,
            quantity: 1,
            image: "/open-book-library.png",
          },
        ],
        pricing: {
          subtotal: 168000,
          shippingFee: 25000,
          discount: 83000,
          total: 110000,
        },
        promoCode: selectedOffer !== null ? offers[selectedOffer].label : undefined,
      }

      console.log("[v0] Sending order data:", orderData)

      const response = await fetch("/api/orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(orderData),
      })

      const result = await response.json()
      console.log("[v0] Order API response:", result)

      if (result.success) {
        setOrderResult({
          orderId: result.orderId,
          message: result.message,
        })

        setShowSuccessModal(true)

        // Clear selected address from localStorage since order is complete
        localStorage.removeItem("selectedAddress")

        if (onPlaceOrder) onPlaceOrder()
      } else {
        setOrderResult({
          message: result.message || "Có lỗi xảy ra khi đặt hàng",
        })
      }
    } catch (error) {
      console.error("[v0] Order creation failed:", error)
      setOrderResult({
        message: "Không thể kết nối đến server. Vui lòng thử lại.",
      })
    } finally {
      setIsProcessingOrder(false)
      setIsValidating(false)
    }
  }

  const offers = [
    {
      label: "Giảm 10%",
      desc: "Tối đa 50K",
      bank: "Áp dụng thẻ Visa",
      highlight: false,
    },
    {
      label: "Giảm 20K",
      desc: "Đơn từ 200K",
      bank: "Áp dụng thẻ Mastercard",
      highlight: false,
    },
    {
      label: "Hoàn 5%",
      desc: "Tối đa 30K",
      bank: "Áp dụng ví điện tử",
      highlight: true,
    },
  ]

  const shippingOptions = [
    {
      id: "express",
      name: "Giao siêu tốc 2h",
      description: "Giao tiết kiệm -16K",
      price: "25.000₫",
      discount: "-5K",
      badge: "NEW",
      badgeColor: "destructive",
      icon: <Zap className="w-5 h-5 text-orange-500" />,
      estimatedTime: "Trước 13h hôm nay",
    },
    {
      id: "fast",
      name: "Giao nhanh",
      description: "Giao trong ngày",
      price: "20.000₫",
      discount: null,
      badge: null,
      badgeColor: null,
      icon: <Truck className="w-5 h-5 text-blue-500" />,
      estimatedTime: "Trong ngày hôm nay",
    },
    {
      id: "standard",
      name: "Giao tiêu chuẩn",
      description: "Giao trong 2-3 ngày",
      price: "15.000₫",
      discount: null,
      badge: "TIẾT KIỆM",
      badgeColor: "secondary",
      icon: <Clock className="w-5 h-5 text-green-500" />,
      estimatedTime: "2-3 ngày làm việc",
    },
  ]

  const paymentMethods = [
    {
      id: "cash",
      name: "Thanh toán tiền mặt",
      description: "Thanh toán khi nhận hàng",
      icon: <CreditCard className="w-5 h-5 text-green-600" />,
      popular: true,
    },
    {
      id: "credit_card",
      name: "Thẻ tín dụng/Ghi nợ",
      description: "Visa, Mastercard, JCB",
      icon: <CreditCard className="w-5 h-5 text-blue-600" />,
      popular: false,
    },
    {
      id: "bank_transfer",
      name: "Chuyển khoản ngân hàng",
      description: "Vietcombank, Techcombank, BIDV",
      icon: <Building2 className="w-5 h-5 text-purple-600" />,
      popular: false,
    },
    {
      id: "viettel",
      name: "Viettel Money",
      description: "Ví điện tử Viettel",
      icon: <Wallet className="w-5 h-5 text-red-600" />,
      popular: false,
    },
    {
      id: "momo",
      name: "Ví MoMo",
      description: "Thanh toán qua ví MoMo",
      icon: <Smartphone className="w-5 h-5 text-pink-600" />,
      popular: true,
    },
    {
      id: "zalopay",
      name: "ZaloPay",
      description: "Thanh toán qua ZaloPay",
      icon: <Smartphone className="w-5 h-5 text-blue-500" />,
      popular: false,
    },
  ]

  if (showAddressForm) {
    return (
      <AddressForm
        address={address}
        onSave={(addr: Address) => {
          setAddress(addr)
          setShowAddressForm(false)
          setErrors((prev) => ({ ...prev, address: undefined }))
        }}
        onCancel={() => setShowAddressForm(false)}
      />
    )
  }

  return (
    <div className="max-w-7xl mx-auto p-4">
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        orderId={orderResult?.orderId || ""}
        message={orderResult?.message || ""}
        onViewOrder={() => {
          if (orderResult?.orderId) {
            navigate(`/orders/${orderResult.orderId}`)
          }
        }}
      />

      {/* Header */}
      <div className="bg-green-100 p-3 rounded-lg mb-6 text-center">
        <p className="text-green-700 text-sm">
          Freeship đơn từ 45k, giảm nhiều hơn cùng <span className="font-bold text-blue-600">FREESHIP XTRA</span>
        </p>
      </div>

      <div className="flex items-center gap-4 mb-6">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-blue-600 text-white rounded flex items-center justify-center font-bold">T</div>
          <span className="text-blue-600 font-bold">TIKI</span>
          <span className="text-gray-500">Tốt & Nhanh</span>
        </div>
        <span className="text-blue-600 font-medium">Thanh toán</span>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                Chọn hình thức giao hàng
                {errors.shipping && <AlertCircle className="w-4 h-4 text-red-500" />}
              </CardTitle>
              {errors.shipping && (
                <div className="flex items-center gap-2 text-red-600 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.shipping}
                </div>
              )}
            </CardHeader>
            <CardContent>
              <RadioGroup
                value={selectedDelivery}
                onValueChange={(value) => {
                  setSelectedDelivery(value)
                  setErrors((prev) => ({ ...prev, shipping: undefined }))
                }}
              >
                <div className="space-y-4">
                  {shippingOptions.map((option) => (
                    <div
                      key={option.id}
                      className={`flex items-center space-x-3 p-4 border rounded-lg transition-colors hover:bg-gray-50 ${selectedDelivery === option.id ? "bg-blue-50 border-blue-200" : ""}`}
                    >
                      <RadioGroupItem value={option.id} id={option.id} />
                      <div className="flex items-center gap-3 flex-1">
                        {option.icon}
                        <Label htmlFor={option.id} className="flex-1 cursor-pointer">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{option.name}</span>
                            {option.badge && (
                              <Badge variant={option.badgeColor as 'default' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | undefined} className="text-xs">
                                {option.badge}
                              </Badge>
                            )}
                            {option.discount && (
                              <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                                {option.discount}
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                          <p className="text-xs text-gray-500 mt-1">{option.estimatedTime}</p>
                        </Label>
                        <div className="text-right">
                          <p className="font-medium text-blue-600">{option.price}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </RadioGroup>

              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Truck className="w-5 h-5 text-green-600" />
                  <span className="text-sm">
                    Đã: {shippingOptions.find((opt) => opt.id === selectedDelivery)?.name},{" "}
                    {shippingOptions.find((opt) => opt.id === selectedDelivery)?.estimatedTime}
                  </span>
                </div>
                <div className="mt-3 flex items-center gap-3">
                  <div className="w-12 h-16 bg-gray-200 rounded flex items-center justify-center">
                    <span className="text-xs">IMG</span>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">Chat GPT Thực Chiến</h4>
                    <p className="text-sm text-gray-600">SL: x1</p>
                    <p className="text-red-600 font-medium">110.000 ₫</p>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <Button variant="ghost" className="text-blue-600 p-0">
                  Thêm sản phẩm khuyến mãi của Shop {">"}
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                Chọn hình thức thanh toán
                {errors.payment && <AlertCircle className="w-4 h-4 text-red-500" />}
              </CardTitle>
              {errors.payment && (
                <div className="flex items-center gap-2 text-red-600 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.payment}
                </div>
              )}
            </CardHeader>
            <CardContent>
              <RadioGroup
                value={selectedPayment}
                onValueChange={(value) => {
                  setSelectedPayment(value)
                  setErrors((prev) => ({ ...prev, payment: undefined }))
                }}
              >
                <div className="space-y-3">
                  {paymentMethods.map((method) => (
                    <div
                      key={method.id}
                      className={`flex items-center space-x-3 p-3 border rounded-lg transition-colors hover:bg-gray-50 ${selectedPayment === method.id ? "bg-blue-50 border-blue-200" : ""}`}
                    >
                      <RadioGroupItem value={method.id} id={method.id} />
                      <div className="flex items-center gap-3 flex-1">
                        {method.icon}
                        <Label htmlFor={method.id} className="flex-1 cursor-pointer">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{method.name}</span>
                            {method.popular && (
                              <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-700">
                                PHỔ BIẾN
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{method.description}</p>
                        </Label>
                      </div>
                    </div>
                  ))}
                </div>
              </RadioGroup>

              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-700">💳 Ưu đãi thanh toán thẻ - Giảm thêm đến 50K</p>
              </div>

              {/* Discount Options */}
              <div className="mt-6">
                <h4 className="font-medium mb-3">Ưu đãi thanh toán</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {offers.map((offer, index) => (
                    <button
                      key={index}
                      type="button"
                      className={`p-3 border rounded-lg text-center cursor-pointer transition hover:border-blue-500 focus:outline-none
  ${selectedOffer === index ? "border-blue-500 bg-blue-50" : ""} ${offer.highlight ? "border-orange-300 bg-orange-50" : ""}`}
                      onClick={() => setSelectedOffer(index)}
                    >
                      <div className="text-blue-600 font-medium text-sm">{offer.label}</div>
                      <div className="text-xs text-gray-600 mt-1">{offer.desc}</div>
                      <div className="text-xs text-gray-500 mt-1">{offer.bank}</div>
                      {offer.highlight && <div className="text-xs text-orange-600 mt-1 font-medium">🔥 HOT</div>}
                    </button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Order Summary */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 flex items-center gap-2">
                  Giao tới
                  {(errors.address || errors.phone) && <AlertCircle className="w-4 h-4 text-red-500" />}
                </span>
                <Button variant="ghost" className="text-blue-600 p-0 h-auto" onClick={() => navigate("/address")}> 
                  Thay đổi
                </Button>
              </div>
              <div>
                <p className="font-medium">
                  {address.name} | {address.phone}
                </p>
                <p className="text-sm text-gray-600">
                  {[address.detail, address.ward, address.district, address.province, "Việt Nam"]
                    .filter(Boolean)
                    .join(", ")}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {address.type === "home"
                    ? "Nhà riêng / Chung cư"
                    : address.type === "office"
                      ? "Cơ quan / Công ty"
                      : ""}
                  {address.isDefault && (
                    <span className="ml-2 text-green-600 border border-green-400 px-2 py-0.5 rounded text-xs bg-green-50">
                      Địa chỉ mặc định
                    </span>
                  )}
                </p>
                {errors.address && (
                  <div className="flex items-center gap-2 text-red-600 text-sm mt-2">
                    <AlertCircle className="w-4 h-4" />
                    {errors.address}
                  </div>
                )}
                {errors.phone && (
                  <div className="flex items-center gap-2 text-red-600 text-sm mt-2">
                    <AlertCircle className="w-4 h-4" />
                    {errors.phone}
                  </div>
                )}
              </div>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <span className="text-sm">Tài Khuyến Mãi</span>
                <span className="text-sm text-gray-600">Có thể chọn 2</span>
              </div>
              <div className="flex gap-2 mt-2">
                <Badge variant="secondary" className="bg-green-100 text-green-700">
                  <Truck className="w-3 h-3 mr-1" />
                  Giảm 25K
                </Badge>
                <Button variant="outline" size="sm" className="text-blue-600 bg-transparent">
                  Chọn hoặc nhập mã khác
                </Button>
              </div>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Đơn hàng</CardTitle>
              <p className="text-sm text-gray-600">1 sản phẩm. Xem thông tin {">"}</p>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>Tạm tính</span>
                <span>168.000₫</span>
              </div>
              <div className="flex justify-between">
                <span>Phí vận chuyển</span>
                <span>25.000₫</span>
              </div>
              <div className="flex justify-between text-green-600">
                <span>Giảm giá trực tiếp</span>
                <span>-58.000₫</span>
              </div>
              <div className="flex justify-between text-green-600">
                <span>Giảm giá vận chuyển</span>
                <span>-25.000₫</span>
              </div>
              <Separator />
              <div className="flex justify-between text-lg font-bold">
                <span>Tổng tiền thanh toán</span>
                <div className="text-right">
                  <div className="text-red-600">110.000 ₫</div>
                  <div className="text-sm text-gray-600">Tiết kiệm 84.000 ₫</div>
                </div>
              </div>
              <p className="text-xs text-gray-500">(Đã bao gồm thuế VAT nếu có)</p>
              <Button
                className={`w-full ${isFormValid() && !isProcessingOrder ? "bg-red-500 hover:bg-red-600" : "bg-gray-400 cursor-not-allowed"} text-white`}
                onClick={onPlaceOrderClick}
                disabled={!isFormValid() || isValidating || isProcessingOrder}
              >
                {isProcessingOrder ? "Đang xử lý đơn hàng..." : isValidating ? "Đang kiểm tra..." : "Đặt hàng"}
              </Button>
              {!isFormValid() && !isProcessingOrder && (
                <p className="text-xs text-red-500 text-center mt-2">Vui lòng kiểm tra và điền đầy đủ thông tin</p>
              )}
              {orderResult && !orderResult.orderId && (
                <div className="text-xs text-center mt-2 p-2 rounded text-red-600 bg-red-50">{orderResult.message}</div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}