"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON>, <PERSON>ge } from "../../../components/ui"
import { Card, CardContent, CardHeader, CardTitle } from "../../../components/ui/Card"
import { Separator } from "../../../components/ui/Separator"
import { ArrowLeft, Package, Truck, CheckCircle, Clock, MapPin, CreditCard, Phone } from "lucide-react"

interface OrderDetails {
  id: string
  status: string
  createdAt: string
  estimatedDelivery: string
  customerInfo: {
    name: string
    phone: string
  }
  shippingAddress: {
    name: string
    phone: string
    detail: string
    ward: string
    district: string
    province: string
    type: string
  }
  shippingMethod: {
    name: string
    price: number
    estimatedTime: string
  }
  paymentMethod: {
    name: string
  }
  items: Array<{
    id: string
    name: string
    price: number
    quantity: number
    image?: string
  }>
  pricing: {
    subtotal: number
    shippingFee: number
    discount: number
    total: number
  }
}

export default function OrderDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const orderId = params.orderId as string

  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (orderId) {
      fetchOrderDetails()
    }
  }, [orderId])

  const fetchOrderDetails = async () => {
    try {
      const response = await fetch(`/api/orders?orderId=${orderId}`)
      const result = await response.json()

      if (result.success) {
        // Mock detailed order data since API returns basic info
        const mockOrderDetails: OrderDetails = {
          id: orderId,
          status: "confirmed",
          createdAt: new Date().toISOString(),
          estimatedDelivery: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          customerInfo: {
            name: "Nguyễn Văn A",
            phone: "0901234567",
          },
          shippingAddress: {
            name: "Nguyễn Văn A",
            phone: "0901234567",
            detail: "123 Đường ABC",
            ward: "Phường 1",
            district: "Quận 1",
            province: "TP. Hồ Chí Minh",
            type: "home",
          },
          shippingMethod: {
            name: "Giao nhanh",
            price: 20000,
            estimatedTime: "Trong ngày hôm nay",
          },
          paymentMethod: {
            name: "Thanh toán tiền mặt",
          },
          items: [
            {
              id: "book-001",
              name: "Chat GPT Thực Chiến",
              price: 110000,
              quantity: 1,
              image: "/open-book-library.png",
            },
          ],
          pricing: {
            subtotal: 168000,
            shippingFee: 25000,
            discount: 83000,
            total: 110000,
          },
        }
        setOrderDetails(mockOrderDetails)
      } else {
        setError("Không tìm thấy đơn hàng")
      }
    } catch {
      setError("Có lỗi xảy ra khi tải thông tin đơn hàng")
    } finally {
      setLoading(false)
    }
  }

  const getStatusInfo = (status: string) => {
    switch (status) {
      case "confirmed":
        return {
          label: "Đã xác nhận",
          color: "bg-green-100 text-green-800",
          icon: <CheckCircle className="w-4 h-4" />,
        }
      case "preparing":
        return {
          label: "Đang chuẩn bị",
          color: "bg-yellow-100 text-yellow-800",
          icon: <Package className="w-4 h-4" />,
        }
      case "shipping":
        return {
          label: "Đang giao hàng",
          color: "bg-blue-100 text-blue-800",
          icon: <Truck className="w-4 h-4" />,
        }
      case "delivered":
        return {
          label: "Đã giao hàng",
          color: "bg-green-100 text-green-800",
          icon: <CheckCircle className="w-4 h-4" />,
        }
      default:
        return {
          label: "Đang xử lý",
          color: "bg-gray-100 text-gray-800",
          icon: <Clock className="w-4 h-4" />,
        }
    }
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-4">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error || !orderDetails) {
    return (
      <div className="max-w-4xl mx-auto p-4">
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-red-600 mb-4">{error || "Không tìm thấy đơn hàng"}</p>
            <Button onClick={() => router.push("/")}>Về trang chủ</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const statusInfo = getStatusInfo(orderDetails.status)

  return (
    <div className="max-w-4xl mx-auto p-4">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Quay lại
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Chi tiết đơn hàng</h1>
          <p className="text-gray-600">Mã đơn hàng: {orderDetails.id}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Order Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Status */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Trạng thái đơn hàng</CardTitle>
                <Badge className={statusInfo.color}>
                  {statusInfo.icon}
                  <span className="ml-1">{statusInfo.label}</span>
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 text-green-600">
                  <CheckCircle className="w-5 h-5" />
                  <div>
                    <p className="font-medium">Đơn hàng đã được xác nhận</p>
                    <p className="text-sm text-gray-500">{new Date(orderDetails.createdAt).toLocaleString("vi-VN")}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 text-gray-400">
                  <Package className="w-5 h-5" />
                  <div>
                    <p className="font-medium">Đang chuẩn bị hàng</p>
                    <p className="text-sm text-gray-500">Dự kiến trong 1-2 giờ</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 text-gray-400">
                  <Truck className="w-5 h-5" />
                  <div>
                    <p className="font-medium">Đang giao hàng</p>
                    <p className="text-sm text-gray-500">
                      Dự kiến: {new Date(orderDetails.estimatedDelivery).toLocaleString("vi-VN")}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Items */}
          <Card>
            <CardHeader>
              <CardTitle>Sản phẩm đã đặt</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {orderDetails.items.map((item) => (
                  <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                    <div className="w-16 h-20 bg-gray-200 rounded flex items-center justify-center">
                      {item.image ? (
                        <img
                          src={item.image || "/placeholder.svg"}
                          alt={item.name}
                          className="w-full h-full object-cover rounded"
                        />
                      ) : (
                        <span className="text-xs">IMG</span>
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{item.name}</h4>
                      <p className="text-sm text-gray-600">Số lượng: {item.quantity}</p>
                      <p className="text-red-600 font-medium">{item.price.toLocaleString("vi-VN")} ₫</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Details */}
        <div className="space-y-6">
          {/* Shipping Address */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Địa chỉ giao hàng
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="font-medium">{orderDetails.shippingAddress.name}</p>
                <p className="text-sm text-gray-600 flex items-center gap-2">
                  <Phone className="w-4 h-4" />
                  {orderDetails.shippingAddress.phone}
                </p>
                <p className="text-sm text-gray-600">
                  {[
                    orderDetails.shippingAddress.detail,
                    orderDetails.shippingAddress.ward,
                    orderDetails.shippingAddress.district,
                    orderDetails.shippingAddress.province,
                  ]
                    .filter(Boolean)
                    .join(", ")}
                </p>
                <Badge variant="secondary" className="text-xs">
                  {orderDetails.shippingAddress.type === "home" ? "Nhà riêng" : "Cơ quan"}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Payment & Shipping */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="w-5 h-5" />
                Thanh toán & Giao hàng
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">Phương thức thanh toán</p>
                <p className="font-medium">{orderDetails.paymentMethod.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Phương thức giao hàng</p>
                <p className="font-medium">{orderDetails.shippingMethod.name}</p>
                <p className="text-xs text-gray-500">{orderDetails.shippingMethod.estimatedTime}</p>
              </div>
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Tổng kết đơn hàng</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>Tạm tính</span>
                <span>{orderDetails.pricing.subtotal.toLocaleString("vi-VN")}₫</span>
              </div>
              <div className="flex justify-between">
                <span>Phí vận chuyển</span>
                <span>{orderDetails.pricing.shippingFee.toLocaleString("vi-VN")}₫</span>
              </div>
              <div className="flex justify-between text-green-600">
                <span>Giảm giá</span>
                <span>-{orderDetails.pricing.discount.toLocaleString("vi-VN")}₫</span>
              </div>
              <Separator />
              <div className="flex justify-between text-lg font-bold">
                <span>Tổng cộng</span>
                <span className="text-red-600">{orderDetails.pricing.total.toLocaleString("vi-VN")} ₫</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}