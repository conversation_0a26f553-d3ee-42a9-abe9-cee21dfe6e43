import { type NextRequest, NextResponse } from "next/server"

interface OrderItem {
  id: string
  name: string
  price: number
  quantity: number
  image?: string
}

interface OrderData {
  customerInfo: {
    name: string
    phone: string
    email?: string
  }
  shippingAddress: {
    name: string
    phone: string
    detail: string
    ward: string
    district: string
    province: string
    type: string
  }
  shippingMethod: {
    id: string
    name: string
    price: number
    estimatedTime: string
  }
  paymentMethod: {
    id: string
    name: string
  }
  items: OrderItem[]
  pricing: {
    subtotal: number
    shippingFee: number
    discount: number
    total: number
  }
  promoCode?: string
  notes?: string
}

interface OrderResponse {
  success: boolean
  orderId?: string
  message: string
  estimatedDelivery?: string
}

export async function POST(request: NextRequest): Promise<NextResponse<OrderResponse>> {
  try {
    const orderData: OrderData = await request.json()

    console.log("[v0] Received order data:", orderData)

    // Validate required fields
    if (!orderData.customerInfo?.name || !orderData.customerInfo?.phone) {
      return NextResponse.json(
        {
          success: false,
          message: "Thông tin khách hàng không đầy đủ",
        },
        { status: 400 },
      )
    }

    if (!orderData.shippingAddress?.detail || !orderData.shippingAddress?.ward) {
      return NextResponse.json(
        {
          success: false,
          message: "Địa chỉ giao hàng không đầy đủ",
        },
        { status: 400 },
      )
    }

    if (!orderData.items || orderData.items.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: "Đơn hàng phải có ít nhất 1 sản phẩm",
        },
        { status: 400 },
      )
    }

    // Simulate order processing delay
    await new Promise((resolve) => setTimeout(resolve, 1500))

    // Generate order ID
    const orderId = `TK${Date.now()}${Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, "0")}`

    // Calculate estimated delivery based on shipping method
    const now = new Date()
    let estimatedDelivery = ""

    switch (orderData.shippingMethod.id) {
      case "express":
        now.setHours(now.getHours() + 2)
        estimatedDelivery = now.toLocaleString("vi-VN")
        break
      case "fast":
        now.setDate(now.getDate() + 1)
        estimatedDelivery = now.toLocaleDateString("vi-VN")
        break
      case "standard":
        now.setDate(now.getDate() + 3)
        estimatedDelivery = now.toLocaleDateString("vi-VN")
        break
      default:
        now.setDate(now.getDate() + 2)
        estimatedDelivery = now.toLocaleDateString("vi-VN")
    }

    console.log("[v0] Order created successfully:", {
      orderId,
      total: orderData.pricing.total,
      estimatedDelivery,
    })

    return NextResponse.json({
      success: true,
      orderId,
      message: "Đặt hàng thành công!",
      estimatedDelivery,
    })
  } catch (error) {
    console.error("[v0] Order creation error:", error)

    return NextResponse.json(
      {
        success: false,
        message: "Có lỗi xảy ra khi đặt hàng. Vui lòng thử lại.",
      },
      { status: 500 },
    )
  }
}

// GET method to retrieve order details (for future use)
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const orderId = searchParams.get("orderId")

  if (!orderId) {
    return NextResponse.json(
      {
        success: false,
        message: "Order ID is required",
      },
      { status: 400 },
    )
  }

  // In a real app, you would fetch from database
  // For now, return mock data
  return NextResponse.json({
    success: true,
    order: {
      id: orderId,
      status: "confirmed",
      createdAt: new Date().toISOString(),
      estimatedDelivery: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    },
  })
}