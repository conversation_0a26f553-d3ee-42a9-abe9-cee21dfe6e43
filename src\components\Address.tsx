"use client"

import type React from "react"
import { useState, useEffect } from "react"
import Button from "./ui/Button"
import { useNavigate } from "react-router-dom"

interface Address {
  id?: number;
  name: string;
  phone: string;
  province: string;
  district: string;
  ward: string;
  detail: string;
  type: "home" | "office";
  isDefault: boolean;
  provinceCode: string;
  districtCode: string;
  wardCode: string;
}


interface Province {
  code: string | number;
  name: string;
}
interface District {
  code: string | number;
  name: string;
}
interface Ward {
  code: string | number;
  name: string;
}

interface AddressFormProps {
  address: Address;
  onSave: (addr: Address) => void;
  onCancel: () => void;
}

interface Address {
  id?: number
  name: string
  phone: string
  province: string
  district: string
  ward: string
  detail: string
  type: "home" | "office"
  isDefault: boolean
  provinceCode: string
  districtCode: string
  wardCode: string
}

const AddressDisplay: React.FC<{
  address: Address
  onEdit: () => void
  onDelete: () => void
  onSelect: () => void
}> = ({ address, onEdit, onDelete, onSelect }) => {
  const [displayNames, setDisplayNames] = useState({
    province: address.province || "",
    district: address.district || "",
    ward: address.ward || "",
  })
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const loadAddressNames = async () => {
      if (
        (displayNames.province && displayNames.district && displayNames.ward) ||
        (!address.provinceCode && !address.districtCode && !address.wardCode)
      ) {
        return
      }

      setIsLoading(true)
      try {
        let provinceName = displayNames.province
        let districtName = displayNames.district
        let wardName = displayNames.ward

        if (address.provinceCode && !provinceName) {
          const provincesRes = await fetch("https://provinces.open-api.vn/api/p/")
          if (provincesRes.ok) {
            const provinces = await provincesRes.json()
            provinceName =
              provinces.find((p: Province) => p.code.toString() === address.provinceCode.toString())?.name ||
              `Tỉnh ${address.provinceCode}`
          } else {
            provinceName = `Tỉnh ${address.provinceCode}`
          }
        }

        if (address.districtCode && !districtName) {
          const provinceRes = await fetch(`https://provinces.open-api.vn/api/p/${address.provinceCode}?depth=2`)
          if (provinceRes.ok) {
            const provinceData = await provinceRes.json()
            districtName =
              provinceData.districts?.find((d: District) => d.code.toString() === address.districtCode.toString())?.name ||
              `Quận ${address.districtCode}`
          } else {
            districtName = `Quận ${address.districtCode}`
          }
        }

        if (address.wardCode && !wardName) {
          const districtRes = await fetch(`https://provinces.open-api.vn/api/d/${address.districtCode}?depth=2`)
          if (districtRes.ok) {
            const districtData = await districtRes.json()
            wardName =
              districtData.wards?.find((w: Ward) => w.code.toString() === address.wardCode.toString())?.name ||
              `Phường ${address.wardCode}`
          } else {
            wardName = `Phường ${address.wardCode}`
          }
        }

        setDisplayNames({
          province: provinceName,
          district: districtName,
          ward: wardName,
        })
      } catch (error) {
        console.log("[v0] Error loading address names:", error)
        setDisplayNames({
          province: address.province || `Tỉnh ${address.provinceCode}`,
          district: address.district || `Quận ${address.districtCode}`,
          ward: address.ward || `Phường ${address.wardCode}`,
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadAddressNames()
  }, [
    address.provinceCode,
    address.districtCode,
    address.wardCode,
    address.province,
    address.district,
    address.ward,
    displayNames.province,
    displayNames.district,
    displayNames.ward
  ])

  const fullAddress = [address.detail, displayNames.ward, displayNames.district, displayNames.province, "Việt Nam"]
    .filter(Boolean)
    .join(", ")

  return (
    <div className="border border-dashed border-[#1a94ff] rounded p-4 mb-2 flex justify-between items-start">
      <div className="flex-1">
        <div className="font-semibold text-lg mb-2">{address.name}</div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-3 text-sm">
          <div>
            <span className="font-medium">Điện thoại:</span> {address.phone}
          </div>
          <div>
            <span className="font-medium">Loại:</span>{" "}
            {address.type === "home" ? "Nhà riêng / Chung cư" : "Cơ quan / Công ty"}
          </div>
        </div>

        <div className="mb-3">
          <div className="font-medium text-sm mb-1">Địa chỉ đầy đủ:</div>
          <div className="text-sm text-gray-700">{fullAddress}</div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-3 text-xs text-gray-600">
          <div>
            <span className="font-medium">Tỉnh/TP:</span>{" "}
            {isLoading ? "Đang tải..." : displayNames.province || `Tỉnh ${address.provinceCode}`}
          </div>
          <div>
            <span className="font-medium">Quận/Huyện:</span>{" "}
            {isLoading ? "Đang tải..." : displayNames.district || `Quận ${address.districtCode}`}
          </div>
          <div>
            <span className="font-medium">Phường/Xã:</span>{" "}
            {isLoading ? "Đang tải..." : displayNames.ward || `Phường ${address.wardCode}`}
          </div>
        </div>

        <div className="flex gap-2 mt-3">
          <button
            className="bg-[#1a94ff] text-white px-4 py-2 rounded text-sm font-medium hover:bg-[#1479c9]"
            onClick={onSelect}
          >
            Giao đến địa chỉ này
          </button>
          <button className="border border-[#d8d8d8] px-4 py-2 rounded text-sm hover:bg-gray-50" onClick={onEdit}>
            Sửa
          </button>
          <button className="bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700" onClick={onDelete}>
            Xóa
          </button>
        </div>
      </div>

      <div className="ml-4 flex flex-col items-end gap-2">
        {address.isDefault && (
          <span className="text-green-600 text-xs border border-green-400 px-2 py-1 rounded bg-green-50">
            Địa chỉ mặc định
          </span>
        )}
      </div>
    </div>
  )
}

export const AddressForm: React.FC<AddressFormProps> = ({ address, onSave, onCancel }) => {
  interface Province {
    code: string | number;
    name: string;
  }
  interface District {
    code: string | number;
    name: string;
  }
  interface Ward {
    code: string | number;
    name: string;
  }
  const [provinces, setProvinces] = useState<Province[]>([])
  const [districts, setDistricts] = useState<District[]>([])
  const [wards, setWards] = useState<Ward[]>([])
  const [provinceCode, setProvinceCode] = useState("")
  const [districtCode, setDistrictCode] = useState("")
  const [wardCode, setWardCode] = useState("")
  const [form, setForm] = useState<Address>(address)

  useEffect(() => {
    setForm(address)
    setProvinceCode(address.provinceCode || "")
    setDistrictCode(address.districtCode || "")
    setWardCode(address.wardCode || "")
  }, [address])

  useEffect(() => {
    fetch("https://provinces.open-api.vn/api/p/")
      .then((res) => res.json())
      .then(setProvinces)
  }, [])

  useEffect(() => {
    if (provinces.length > 0 && address.provinceCode && !form.province) {
  const provinceName = provinces.find((p) => p.code === address.provinceCode)?.name || ""
  setForm((prev) => ({ ...prev, province: provinceName }))
    }
  }, [provinces, address.provinceCode, form.province])

  useEffect(() => {
    if (provinceCode) {
      fetch(`https://provinces.open-api.vn/api/p/${provinceCode}?depth=2`)
        .then((res) => res.json())
        .then((data) => {
          setDistricts(data.districts || [])
          if (address.districtCode && !form.district) {
            const districtName = data.districts?.find((d: District) => d.code === address.districtCode)?.name || ""
            setForm((prev) => ({ ...prev, district: districtName }))
          }
        })

      if (provinceCode !== address.provinceCode) {
        setDistrictCode("")
        setWardCode("")
        setWards([])
        setForm((prev) => ({
          ...prev,
          province: provinces.find((p) => p.code === provinceCode)?.name || "",
          district: "",
          ward: "",
        }))
      } else {
        setForm((prev) => ({
          ...prev,
          province: provinces.find((p) => p.code === provinceCode)?.name || "",
        }))
      }
    }
    // eslint-disable-next-line
  }, [provinceCode])

  useEffect(() => {
    if (districtCode) {
      fetch(`https://provinces.open-api.vn/api/d/${districtCode}?depth=2`)
        .then((res) => res.json())
        .then((data) => {
          setWards(data.wards || [])
          if (address.wardCode && !form.ward) {
            const wardName = data.wards?.find((w: Ward) => w.code === address.wardCode)?.name || ""
            setForm((prev) => ({ ...prev, ward: wardName }))
          }
        })

      if (districtCode !== address.districtCode) {
        setWardCode("")
        setForm((prev) => ({
          ...prev,
          district: districts.find((d) => d.code === districtCode)?.name || "",
          ward: "",
        }))
      } else {
        setForm((prev) => ({
          ...prev,
          district: districts.find((d) => d.code === districtCode)?.name || "",
        }))
      }
    }
    // eslint-disable-next-line
  }, [districtCode])

  useEffect(() => {
    if (wardCode) {
      setForm((prev) => ({
        ...prev,
        ward: wards.find((w) => w.code === wardCode)?.name || "",
      }))
    }
    // eslint-disable-next-line
  }, [wardCode])

  const handleProvinceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const code = e.target.value
    setProvinceCode(code)
  }

  const handleDistrictChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const code = e.target.value
    setDistrictCode(code)
  }

  const handleWardChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const code = e.target.value
    setWardCode(code)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    console.log("[v0] handleSubmit - provinces array:", provinces.length)
    console.log("[v0] handleSubmit - districts array:", districts.length)
    console.log("[v0] handleSubmit - wards array:", wards.length)
    console.log("[v0] handleSubmit - codes:", { provinceCode, districtCode, wardCode })

    const provinceName = provinces.find((p) => p.code.toString() === provinceCode.toString())?.name || ""
    const districtName = districts.find((d) => d.code.toString() === districtCode.toString())?.name || ""
    const wardName = wards.find((w) => w.code.toString() === wardCode.toString())?.name || ""

    console.log("[v0] Saving address with names:", { provinceName, districtName, wardName })

    const finalProvinceName = provinceName || form.province || ""
    const finalDistrictName = districtName || form.district || ""
    const finalWardName = wardName || form.ward || ""

    console.log("[v0] Final names to save:", { finalProvinceName, finalDistrictName, finalWardName })

    onSave({
      ...form,
      province: finalProvinceName,
      district: finalDistrictName,
      ward: finalWardName,
      provinceCode,
      districtCode,
      wardCode,
    })
  }

  return (
    <form className="grid grid-cols-1 md:grid-cols-[180px_1fr] gap-0" onSubmit={handleSubmit}>
      <div className="hidden md:block bg-[#fafafd] rounded-l-[8px] border-r border-[#e5e5e5] py-8 px-6"></div>
      <div className="p-8">
        <div className="grid grid-cols-1 gap-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-[13px] font-semibold text-[#38383d] mb-2 text-right md:text-left">
                Họ tên
              </label>
              <input
                className="border border-[#d8d8d8] rounded-[4px] px-3 py-2 w-full text-sm placeholder-[#bdbdbd] focus:border-[#1a94ff] outline-none"
                value={form.name}
                onChange={(e) => setForm({ ...form, name: e.target.value })}
                placeholder="Nhập họ tên"
                required
              />
            </div>
            <div>
              <label className="block text-[13px] font-semibold text-[#38383d] mb-2 text-right md:text-left">
                Điện thoại di động
              </label>
              <input
                className="border border-[#d8d8d8] rounded-[4px] px-3 py-2 w-full text-sm placeholder-[#bdbdbd] focus:border-[#1a94ff] outline-none"
                value={form.phone}
                onChange={(e) => setForm({ ...form, phone: e.target.value })}
                placeholder="Nhập số điện thoại"
                required
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-[13px] font-semibold text-[#38383d] mb-2 text-right md:text-left">
                Tỉnh/Thành phố
              </label>
              <select
                className="border border-[#d8d8d8] rounded-[4px] px-3 py-2 w-full text-sm focus:border-[#1a94ff] outline-none"
                value={provinceCode}
                onChange={handleProvinceChange}
                required
              >
                <option value="">Chọn Tỉnh/Thành phố</option>
                {provinces.map((p) => (
                  <option key={p.code} value={p.code}>
                    {p.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-[13px] font-semibold text-[#38383d] mb-2 text-right md:text-left">
                Quận/Huyện
              </label>
              <select
                className="border border-[#d8d8d8] rounded-[4px] px-3 py-2 w-full text-sm focus:border-[#1a94ff] outline-none"
                value={districtCode}
                onChange={handleDistrictChange}
                required
                disabled={!provinceCode}
              >
                <option value="">Chọn Quận/Huyện</option>
                {districts.map((d) => (
                  <option key={d.code} value={d.code}>
                    {d.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-[13px] font-semibold text-[#38383d] mb-2 text-right md:text-left">
                Phường/Xã
              </label>
              <select
                className="border border-[#d8d8d8] rounded-[4px] px-3 py-2 w-full text-sm focus:border-[#1a94ff] outline-none"
                value={wardCode}
                onChange={handleWardChange}
                required
                disabled={!districtCode}
              >
                <option value="">Chọn Phường/Xã</option>
                {wards.map((w) => (
                  <option key={w.code} value={w.code}>
                    {w.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div>
            <label className="block text-[13px] font-semibold text-[#38383d] mb-2 text-right md:text-left">
              Địa chỉ
            </label>
            <textarea
              className="border border-[#d8d8d8] rounded-[4px] px-3 py-2 w-full text-sm placeholder-[#bdbdbd] focus:border-[#1a94ff] outline-none resize-none"
              value={form.detail}
              onChange={(e) => setForm({ ...form, detail: e.target.value })}
              placeholder="Ví dụ: 52, đường Trần Hưng Đạo"
              required
              rows={2}
            />
          </div>
          <div>
            <span className="block text-xs text-[#808089] mb-2">
              Để nhận hàng thuận tiện hơn, bạn vui lòng cho Tiki biết loại địa chỉ.
            </span>
            <span className="block text-[13px] font-semibold text-[#38383d] mb-2">Loại địa chỉ</span>
            <div className="flex gap-8 mb-2">
              <label className="flex items-center gap-2 text-sm text-[#38383d]">
                <input
                  type="radio"
                  checked={form.type === "home"}
                  onChange={() => setForm({ ...form, type: "home" })}
                  className="accent-[#1a94ff] w-4 h-4"
                />{" "}
                Nhà riêng / Chung cư
              </label>
              <label className="flex items-center gap-2 text-sm text-[#38383d]">
                <input
                  type="radio"
                  checked={form.type === "office"}
                  onChange={() => setForm({ ...form, type: "office" })}
                  className="accent-[#1a94ff] w-4 h-4"
                />{" "}
                Cơ quan / Công ty
              </label>
            </div>
            <label className="flex items-center gap-2 text-sm text-[#38383d]">
              <input
                type="checkbox"
                checked={form.isDefault}
                onChange={(e) => setForm({ ...form, isDefault: e.target.checked })}
                className="accent-[#1a94ff] w-4 h-4"
              />
              Sử dụng địa chỉ này làm mặc định.
            </label>
          </div>
          <div className="flex gap-3 mt-6">
            <Button
              type="button"
              variant="outline"
              className="border border-[#d8d8d8] text-[#38383d] bg-white hover:bg-[#f5f5fa] rounded-[4px] px-8 py-2 font-medium"
              onClick={onCancel}
            >
              Hủy bỏ
            </Button>
            <Button
              type="submit"
              className="bg-[#1a94ff] text-white hover:bg-[#1479c9] rounded-[4px] px-8 py-2 font-medium"
            >
              Lưu địa chỉ
            </Button>
          </div>
        </div>
      </div>
    </form>
  )
}

// ---------- Trang quản lý địa chỉ ----------

export default function Address() {
  const [showForm, setShowForm] = useState(false)
  const [addresses, setAddresses] = useState<Address[]>([])
  const [editing, setEditing] = useState<Address | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [shouldSave, setShouldSave] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("addressList")
      console.log("[v0] Loading addresses from localStorage:", saved)
      if (saved && saved !== "null") {
        try {
          const parsedAddresses = JSON.parse(saved)
          if (Array.isArray(parsedAddresses) && parsedAddresses.length > 0) {
            setAddresses(parsedAddresses)
            console.log("[v0] Successfully loaded addresses:", parsedAddresses)
          }
        } catch (error) {
          console.log("[v0] Error parsing saved addresses:", error)
        }
      }
      setIsLoaded(true)
    }
  }, [])

  useEffect(() => {
    if (typeof window !== "undefined" && isLoaded && shouldSave) {
      console.log("[v0] Saving addresses to localStorage:", addresses)
      localStorage.setItem("addressList", JSON.stringify(addresses))
    }
  }, [addresses, isLoaded, shouldSave])

  const handleSave = (addr: Address) => {
    console.log("[v0] Saving address:", addr)
    setShouldSave(true)
    setAddresses((prev) => {
      let newList
      if (editing) {
        console.log("[v0] Editing existing address with ID:", editing.id)
        if (addr.isDefault) {
          newList = prev.map((a) => (a.id === editing.id ? { ...addr, id: editing.id } : { ...a, isDefault: false }))
        } else {
          newList = prev.map((a) => (a.id === editing.id ? { ...addr, id: editing.id } : a))
        }
      } else {
        console.log("[v0] Adding new address")
        const newId = Date.now()
        if (addr.isDefault) {
          newList = [...prev.map((a) => ({ ...a, isDefault: false })), { ...addr, id: newId }]
        } else {
          newList = [...prev, { ...addr, id: newId }]
        }
      }
      console.log("[v0] New address list:", newList)
      return newList
    })
    setShowForm(false)
    setEditing(null)
  }

  const handleDelete = (id: number) => {
    console.log("[v0] Deleting address with ID:", id)
    setShouldSave(true)
    setAddresses((prev) => {
      const newList = prev.filter((a) => a.id !== id)
      console.log("[v0] Address list after deletion:", newList)
      return newList
    })
  }

  const handleSelectAddress = (addr: Address) => {
    console.log("[v0] Selecting address for checkout:", addr)
    localStorage.setItem("selectedAddress", JSON.stringify(addr))
    navigate("/checkout")
  }

  if (!isLoaded) {
    return (
      <div className="w-full min-h-screen bg-[#f5f5fa] py-8 flex items-center justify-center">
        <div className="text-center">
          <div className="text-lg">Đang tải...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full min-h-screen bg-[#f5f5fa] py-8">
      <div className="max-w-4xl mx-auto">
        {!showForm && (
          <>
            <div className="bg-white rounded-[8px] border border-[#e5e5e5] p-6 mb-6">
              <div className="text-sm mb-4 font-medium">Chọn địa chỉ giao hàng có sẵn bên dưới:</div>
              {addresses.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>Chưa có địa chỉ nào được lưu.</p>
                  <p>Hãy thêm địa chỉ giao hàng mới bên dưới.</p>
                </div>
              ) : (
                addresses.map((addr) => (
                  <AddressDisplay
                    key={addr.id}
                    address={addr}
                    onEdit={() => {
                      setEditing(addr)
                      setShowForm(true)
                    }}
                    onDelete={() => { if (typeof addr.id === "number") handleDelete(addr.id) }}
                    onSelect={() => handleSelectAddress(addr)}
                  />
                ))
              )}
            </div>
            <div className="mb-4 text-sm">
              Bạn muốn giao hàng đến địa chỉ khác?{" "}
              <button
                className="text-[#1a94ff] underline font-medium"
                onClick={() => {
                  setEditing(null)
                  setShowForm(true)
                }}
              >
                Thêm địa chỉ giao hàng mới
              </button>
            </div>
          </>
        )}
        {showForm && (
          <AddressForm
            address={
              editing || {
                name: "",
                phone: "",
                province: "",
                district: "",
                ward: "",
                detail: "",
                type: "home",
                isDefault: false,
                provinceCode: "",
                districtCode: "",
                wardCode: "",
              }
            }
            onSave={handleSave}
            onCancel={() => {
              setShowForm(false)
              setEditing(null)
            }}
          />
        )}
      </div>
    </div>
  )
}